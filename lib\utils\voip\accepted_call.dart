import 'package:avatar_glow/avatar_glow.dart';
import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/components/common/common_text_field.dart';
import 'package:ddone/components/numpad.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/widgets/audio_device_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phone_state/phone_state.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';

class AcceptedCallDialog extends StatefulWidget {
  final StopWatchTimer stopWatchTimer;
  final String? caller, callerID;
  final void Function(String value) onDecline;

  AcceptedCallDialog({
    this.caller,
    this.callerID,
    required this.onDecline,
    super.key,
  }) : stopWatchTimer = sl.get<StopWatchTimer>();

  @override
  State<AcceptedCallDialog> createState() => _AcceptedCallDialogState();
}

class _AcceptedCallDialogState extends State<AcceptedCallDialog> {
  late HomeCubit _homeCubit;
  late VoipCubit _voipCubit;

  late TextEditingController _numberController;

  late ScrollController _scrollController;

  String _displayTime = '0:00';

  bool showNumpad = false;

  @override
  void initState() {
    super.initState();

    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _voipCubit = BlocProvider.of<VoipCubit>(context);

    _numberController = TextEditingController();

    _scrollController = ScrollController();

    _numberController.addListener(() {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      removeWhenNotCalling();
    });
  }

  @override
  void dispose() {
    _numberController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void removeWhenNotCalling() {
    if (_voipCubit.state is! VoipSipAccepted && mounted) {
      pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<VoipCubit, VoipState>(
      listener: (context, voipState) {
        if (voipState is VoipSipHungUp) {
          widget.onDecline.call(_displayTime);

          popUntilInitial();
        } else if (voipState is VoipSipHangupError) {
          popUntilInitial();
        }
      },
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          final colorTheme = themeState.colorTheme;
          final textTheme = themeState.themeData.textTheme;
          return StreamBuilder<PhoneState>(
              stream: isMobile ? PhoneState.stream : null,
              builder: (context, snapshot) {
                if (isMobile) {
                  return _buildDialogContent(context, themeState, colorTheme, textTheme, snapshot.data);
                } else {
                  return _buildDialogContent(context, themeState, colorTheme, textTheme, null);
                }
              });
        },
      ),
    );
  }

  Widget _buildDialogContent(
      BuildContext context, ThemeState themeState, ColorTheme colorTheme, TextTheme textTheme, PhoneState? status) {
    if (status?.status == PhoneStateStatus.CALL_INCOMING) {
      _homeCubit.hold();

      if (context.mounted) {
        showOnholdCallDialog(
          context,
          onTap: () async {
            await _homeCubit.unhold();
            pop();
          },
        );
      }
    }

    return AlertDialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(32.0),
        ),
      ),
      insetPadding: const EdgeInsets.all(10),
      backgroundColor: const Color.fromARGB(255, 54, 54, 54),
      content: SizedBox(
        width: context.deviceWidth(isDesktop ? 0.5 : 1.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // AvatarGlow section
            showNumpad
                ? const SizedBox.shrink()
                : AvatarGlow(
                    glowColor: const Color.fromARGB(255, 109, 109, 109),
                    child: Material(
                      elevation: 3.0,
                      shape: const CircleBorder(),
                      child: CircleAvatar(
                        radius: context.responsiveSize<double>(
                          moileSize: 70,
                          tabletSize: 70,
                          desktopSize: context.deviceWidth(0.03),
                          largeScreenSize: context.deviceWidth(0.02),
                        ),
                        backgroundColor: const Color.fromARGB(255, 83, 83, 83),
                        child: Icon(
                          Icons.call,
                          color: Colors.orange,
                          size: context.responsiveSize<double>(
                            moileSize: 70,
                            tabletSize: 70,
                            desktopSize: context.deviceWidth(0.03),
                            largeScreenSize: context.deviceWidth(0.02),
                          ),
                        ),
                      ),
                    ),
                  ),
            // Caller information section
            _buildCallerInfoSection(context, colorTheme, textTheme),
            // Dynamic middle section - loading or call controls
            BlocBuilder<VoipCubit, VoipState>(
              builder: (context, voipState) {
                if (voipState is VoipSipAcceptedLoading) {
                  return _buildLoadingSection();
                } else {
                  return _buildCallControlsSection(context, colorTheme, textTheme);
                }
              },
            ),
            // Hangup button section
            _buildHangupSection(context, colorTheme),
          ],
        ),
      ),
    );
  }

  Widget _buildCallerInfoSection(BuildContext context, ColorTheme colorTheme, TextTheme textTheme) {
    return Column(
      children: [
        Text(
          widget.callerID ?? 'Unknown Caller',
          style: textTheme.displaySmall!.copyWith(color: colorTheme.onBackgroundColor),
        ),
        SizedBox(height: context.deviceHeight(0.02)),
        BlocBuilder<VoipCubit, VoipState>(
          builder: (context, voipState) {
            return Text(
              voipState.statusMessage,
              style: const TextStyle(color: Colors.orange),
            );
          },
        ),
        BlocBuilder<VoipCubit, VoipState>(
          builder: (context, voipState) {
            // Only show timer when not loading and not showing numpad
            if (voipState is VoipSipAcceptedLoading || showNumpad) {
              return const SizedBox.shrink();
            }
            return StreamBuilder<int>(
              stream: widget.stopWatchTimer.rawTime,
              initialData: 0,
              builder: (context, snap) {
                final value = snap.data;

                _displayTime = StopWatchTimer.getDisplayTime(value!);

                return Column(
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Text(
                        _displayTime,
                        style: const TextStyle(
                          fontSize: 15,
                        ),
                      ),
                    ),
                  ],
                );
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildLoadingSection() {
    return const Padding(
      padding: EdgeInsets.symmetric(vertical: spacingExtraLarge),
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
      ),
    );
  }

  Widget _buildCallControlsSection(BuildContext context, ColorTheme colorTheme, TextTheme textTheme) {
    return showNumpad
        ? Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              BlocBuilder<ThemeCubit, ThemeState>(
                builder: (context, themeState) {
                  final colorTheme = themeState.colorTheme;
                  final textTheme = themeState.themeData.textTheme;

                  return CommonTextField(
                    readOnly: true,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    textEditingController: _numberController,
                    scrollController: _scrollController,
                    textFieldHeight: heightLarge,
                    textAlign: TextAlign.center,
                    textStyle: textTheme.displayLarge!.copyWith(color: colorTheme.onPrimaryColor),
                  );
                },
              ),
              const SizedBox(height: spacingExtraLarge),
              Numpad(
                callBack: (value) {
                  setState(() {
                    _numberController.text += value;
                  });

                  _homeCubit.sendDTMF(value);
                },
              ),
            ],
          )
        : Column(
            children: [
              Wrap(
                alignment: WrapAlignment.center,
                children: [
                  RoundShapeInkWell(
                    onTap: () {
                      showTransferDialpadDialog(context);
                    },
                    color: colorTheme.primaryColor,
                    contentWidget: const Icon(Icons.phone_forwarded),
                  ),
                  BlocBuilder<HomeCubit, HomeState>(
                    builder: (context, homeState) {
                      return RoundShapeInkWell(
                        onTap: () {
                          _homeCubit.toggleMic();
                        },
                        color: colorTheme.primaryColor,
                        contentWidget: homeState.isMicOn ? const Icon(Icons.mic) : const Icon(Icons.mic_off),
                      );
                    },
                  ),
                  RoundShapeInkWell(
                    color: colorTheme.primaryColor,
                    contentWidget: AudioDeviceSelector(
                      colorTheme: colorTheme,
                    ),
                  ),
                  RoundShapeInkWell(
                    onTap: () async {
                      await _homeCubit.hold();

                      if (context.mounted) {
                        showOnholdCallDialog(
                          context,
                          onTap: () async {
                            await _homeCubit.unhold();
                            pop();
                          },
                        );
                      }
                    },
                    color: colorTheme.primaryColor,
                    contentWidget: const Icon(Icons.pause),
                  ),
                ],
              ),
              RoundShapeInkWell(
                onTap: () {
                  setState(() {
                    showNumpad = true;
                  });
                },
                color: colorTheme.primaryColor,
                contentWidget: const Icon(Icons.dialpad),
              ),
            ],
          );
  }

  Widget _buildHangupSection(BuildContext context, ColorTheme colorTheme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        const Spacer(),
        Padding(
          padding: const EdgeInsets.all(0.0),
          child: RoundShapeInkWell(
            onTap: () async {
              await _homeCubit.hangup();
              if (context.mounted) {
                popUntilInitial();
              }
            },
            checkNetwork: false,
            color: colorTheme.errorColor,
            contentWidget: const Icon(Icons.call_end),
          ),
        ),
        Expanded(
          child: showNumpad
              ? Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton(
                      onPressed: () {
                        setState(() {
                          showNumpad = false;
                        });
                      },
                      child: const Text('Hide Numpad')),
                )
              : const SizedBox.shrink(),
        ),
      ],
    );
  }
}
