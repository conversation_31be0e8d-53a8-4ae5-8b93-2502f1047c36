import 'dart:async';
import 'dart:math';

import 'package:ddone/service_locator.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/shared_preferences_service.dart';
import 'package:ddone/utils/notification_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:windows_notification/notification_message.dart';

const androidNotificationHighChannelId = 'high_importance_channel';
const androidNotificationHighChannelName = 'High Importance Notifications';
const androidNotificationHighChannelDesc = 'This channel is used for important notifications.';
const androidNotificationMaxChannelImportance = Importance.low;
const int int32MaxValue = 2147483647;

class NotificationService {
  late final FlutterLocalNotificationsPlugin localNotificationsPlugin;
  late final CallkitService callkitService;
  late final SharedPreferencesService prefs;

  NotificationService()
      : localNotificationsPlugin = sl.get<FlutterLocalNotificationsPlugin>(),
        callkitService = sl.get<CallkitService>(),
        prefs = sl.get<SharedPreferencesService>();

  String? _activeChatId;
  final String _notificationIdPrefix = 'notfid_';

  Future<void> initializeLocalNotifications({
    Function(NotificationResponse)? onSelectNotification,
    Function(NotificationResponse)? onBackgroundSelectNotification,
  }) async {
    await localNotificationsPlugin.initialize(
      InitializationSettings(
        android: const AndroidInitializationSettings('@mipmap/ic_launcher'),
        iOS: DarwinInitializationSettings(
          notificationCategories: iosMacosNotificationTemplate,
          requestAlertPermission: false,
          requestBadgePermission: false,
          requestSoundPermission: false,
        ),
        macOS: DarwinInitializationSettings(
          notificationCategories: iosMacosNotificationTemplate,
          requestAlertPermission: false,
          requestBadgePermission: false,
          requestSoundPermission: false,
        ),
      ),
      onDidReceiveNotificationResponse: onSelectNotification,
      onDidReceiveBackgroundNotificationResponse: onBackgroundSelectNotification,
    );

    // This channel is created to enable foreground notifications for Android
    await localNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(
          const AndroidNotificationChannel(
            androidNotificationHighChannelId,
            androidNotificationHighChannelName,
            description: androidNotificationHighChannelDesc,
            importance: androidNotificationMaxChannelImportance,
          ),
        );
  }

  Future<void> showLocalNotification({
    int? notificationId,
    required String title,
    required String desc,
    required String payload,
    AndroidNotificationDetails? androidNotificationDetails,
    List<AndroidNotificationAction>? androidNotificaitonAction,
    DarwinNotificationDetails? darwinNotificationDetails,
    NotificationMessage? windowNotificationMessage,
    String? windowsNotificationTemplate,
  }) async {
    if (isWindows) {
      // final windowsLocalNotification = sl.get<WindowsNotification>();

      // NotificationMessage notificationMessage = NotificationMessage.fromPluginTemplate(
      //   "test1",
      //   title,
      //   message,
      //   largeImage: '',
      //   image: '',
      // );

      // windowsLocalNotification.showNotificationPluginTemplate(notificationMessage);
      // windowsLocalNotification.showNotificationCustomTemplate(
      //   windowNotificationMessage ??
      //       NotificationMessage.fromPluginTemplate(
      //         '0',
      //         'title',
      //         'body',
      //       ),
      //   windowsNotificationTemplate ?? '',
      // );

      return;
    }

    await localNotificationsPlugin.show(
      notificationId ?? Random().nextInt(int32MaxValue),
      title,
      desc,
      NotificationDetails(
        android: androidNotificationDetails ??
            AndroidNotificationDetails(
              androidNotificationHighChannelId,
              androidNotificationHighChannelName,
              channelDescription: androidNotificationHighChannelDesc,
              visibility: NotificationVisibility.public,
              priority: Priority.max,
              importance: Importance.max,
              actions: androidNotificaitonAction,
            ),
        iOS: darwinNotificationDetails,
        macOS: darwinNotificationDetails,
      ),
      payload: payload,
    );
  }

  Future<void> showAttachmentLocalNotification({
    int? notificationId,
    required String title,
    required String messageType,
    required String message,
    required String payload,
    AndroidNotificationDetails? androidNotificationDetails,
    List<AndroidNotificationAction>? androidNotificaitonAction,
    DarwinNotificationDetails? darwinNotificationDetails,
    NotificationMessage? windowNotificationMessage,
    String? windowsNotificationTemplate,
  }) async {
    String desc = _getAttachmentDescription(messageType, message);

    await showLocalNotification(
      notificationId: notificationId,
      title: title,
      desc: desc,
      payload: payload,
      androidNotificationDetails: androidNotificationDetails,
      androidNotificaitonAction: androidNotificaitonAction,
      darwinNotificationDetails: darwinNotificationDetails,
      windowNotificationMessage: windowNotificationMessage,
      windowsNotificationTemplate: windowsNotificationTemplate,
    );
  }

  /// Get the processed description for attachment notifications
  String getAttachmentDescription(String messageType, String message) {
    return _getAttachmentDescription(messageType, message);
  }

  String _getAttachmentDescription(String messageType, String message) {
    if (messageType == 'Text') {
      // Check if the text message contains a URL that points to a file
      final String trimmedBody = message.trim();
      final Uri? fileUri = Uri.tryParse(trimmedBody);

      // Only process if it's a valid URL
      if (fileUri != null && fileUri.hasScheme && fileUri.pathSegments.isNotEmpty) {
        final RegExp imageExtensions = RegExp(r'\.(jpg|jpeg|png|gif|bmp|webp|svg|ico)$', caseSensitive: false);
        final RegExp videoExtensions = RegExp(r'\.(mp4|mkv|mov|avi|wmv|flv|webm|m4v)$', caseSensitive: false);
        final isVideo = videoExtensions.hasMatch(trimmedBody);
        final isImage = imageExtensions.hasMatch(trimmedBody);

        if (isImage) {
          return 'Image';
        } else if (isVideo) {
          return 'Video';
        } else {
          return 'Document';
        }
      }

      // If it's not a file URL, return the original message
      return message;
    }

    // For non-text message types, check the message content
    final String trimmedBody = message.trim();
    final Uri? fileUri = Uri.tryParse(trimmedBody);
    final RegExp imageExtensions = RegExp(r'\.(jpg|jpeg|png|gif|bmp|webp|svg|ico)$', caseSensitive: false);
    final RegExp videoExtensions = RegExp(r'\.(mp4|mkv|mov|avi|wmv|flv|webm|m4v)$', caseSensitive: false);
    final isVideo = videoExtensions.hasMatch(trimmedBody);
    final isImage = imageExtensions.hasMatch(trimmedBody);

    if (isImage) {
      return '🖼️ Image';
    } else if (isVideo) {
      return '🎥 Video';
    } else if (fileUri != null && fileUri.hasScheme && fileUri.pathSegments.isNotEmpty) {
      return '📄 Document';
    }

    return messageType;
  }

  Future<NotificationAppLaunchDetails?> getNotificationAppLaunchDetails() async {
    return await localNotificationsPlugin.getNotificationAppLaunchDetails();
  }

  Future<bool> requestNotificationPermission() async {
    bool result = false;

    if (isIOS) {
      result = await localNotificationsPlugin
              .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
              ?.requestPermissions(
                alert: true,
                badge: true,
                sound: true,
                provisional: true,
              ) ??
          false;
    } else if (isMacOS) {
      result = await localNotificationsPlugin
              .resolvePlatformSpecificImplementation<MacOSFlutterLocalNotificationsPlugin>()
              ?.requestPermissions(
                alert: true,
                badge: true,
                sound: true,
                provisional:
                    true, //This type of permission system allows for notification permission to be instantly granted without displaying a dialog to your user
              ) ??
          false;
    }

    return result;
  }

  void setActiveChat(String chatId) {
    _activeChatId = chatId;
  }

  void removeActiveChat() {
    _activeChatId = null;
  }

  bool isChatActive(String chatId) {
    return _activeChatId == chatId;
  }

  bool hasProcessedNotification(String id) {
    String? n = prefs.getString(_notificationIdPrefix + id);
    return n != null;
  }

  void saveNotificationId(String id) async {
    await prefs.setString(_notificationIdPrefix + id, id, ttlInSeconds: const Duration(days: 1).inSeconds);
  }
}
