import 'dart:async';

import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/events/network_event.dart';
import 'package:ddone/events/voip_event.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/enums/voip_sip_event_enum.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/voip_service.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:equatable/equatable.dart';
import 'package:event_bus_plus/res/event_bus.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

part 'voip_state.dart';

class VoipCubit extends Cubit<VoipState> with PrefsAware {
  late final VoipService _voipService;
  late final IEventBus _eventBus;

  StreamSubscription<VoipEvent>? _voipEventSubscription;
  StreamSubscription<NetworkEvent>? _networkEventSubscription;

  RTCVideoRenderer get remoteVideoRenderer => _voipService.janusService.remoteVideoRenderer;

  VoipCubit._({
    VoipState? state,
  })  : _voipService = sl.get<VoipService>(),
        _eventBus = sl.get<IEventBus>(),
        super(state ?? const VoipInitial()) {
    _voipEventSubscription = _eventBus.on<VoipEvent>().listen((event) {
      log.t('voipCubit evenbus: ${event.state}');
      emitVoipEvent(event.state);
    });
    _networkEventSubscription = _eventBus.on<NetworkEvent>().listen((event) {
      if (event.state is NetworkReconnected) {
        log.t('VoipCubit - eventBus: networkEvent:$event');
        _voipService.maintainConnection();
      } else if (event.state is NetworkDisconnected) {
        _hangupCallWhenNetworkDisconnect();
      }
    });
  }

  factory VoipCubit.initial({VoipState? state}) {
    return sl.isRegistered<VoipCubit>() ? sl.get<VoipCubit>() : VoipCubit._(state: state);
  }

  Future<void> emitVoipEvent(VoipState eventBusState) async {
    if (eventBusState is VoipSipIncomingCall) {
      // 1. ignore incoming call event if already in call
      // 2. when it is hanging up, it may still need to wait for incoming call first then can only decline/hangup call
      //    so in such case incoming call is part of the hanging up process.
      if (state is VoipSipAccepted || state is VoipSipHangingUp) return;
    } else if (eventBusState is VoipSipAccepted) {
      _preventScreenOff(true);
    } else if (eventBusState is VoipSipHangup) {
      _preventScreenOff(false);
    } else if (eventBusState is VoipSipRegistered) {
      // when ttl between fusion and janus expired, janus will automatically re-register.
      // we must NOT overwrite the state when it is in call.
      if (state is VoipSipCalling ||
          state is VoipSipProceeding ||
          state is VoipSipRinging ||
          state is VoipSipAccepted ||
          state is VoipSipProgress ||
          state is VoipSipIncomingCall) return;
    }
    emit(eventBusState);
  }

  Future<void> logout() async {
    await dispose();
    // TODO: temp fix, janus listener wont trigger unregister not sure why
    // - Seems like something wrong with Janus server, janus_client package doesn't receive any response from it.
    //   Checked - Janus server received SIP REGISTER event with expried=0 from fusionPBX.
    //           - Janus server log print our 'successfully unregistered'.
    emit(const VoipSipUnregistered());
  }

  void _preventScreenOff(bool enabled) {
    try {
      if (isAndroid) {
        WakelockPlus.toggle(enable: enabled);
      }
    } catch (e) {
      log.e('failed to toggle wakelock', error: e);
    }
  }

  /// When we accept call from background isolate, it take some to invoke foreground acceptCall method.
  /// This will immediately show the loading screen when user start the app.
  void checkAcceptingCallFromBackground() async {
    if (prefs.getBool(CacheKeys.acceptedCallFromBackground) ?? false) {
      prefs.remove(CacheKeys.acceptedCallFromBackground);
      Map<String, String> callInfo = await _voipService.callkitService.getCallerInfo();
      emit(VoipSipAcceptedLoading(
          statusMessage: VoipSipEvent.loading.statusMessage(),
          caller: callInfo['caller'],
          callerId: callInfo['callerId'],
          callee: callInfo['caller'],
          calleeId: callInfo['callerId']));
    }
  }

  void _hangupCallWhenNetworkDisconnect() async {
    if (await _voipService.callkitService.hasActiveCalls()) {
      EasyLoadingService()
          .showErrorWithText('Call ended due to network disconnect.', duration: const Duration(seconds: 7));
    }
  }

  Future<void> dispose() async {
    await _voipService.janusService.stopStreams();
    await _voipService.janusService.disposeMedia();
    await _voipService.dispose();
  }

  @override
  Future<void> close() async {
    try {
      await dispose();
    } catch (e) {
      log.e('Failed to dispose VoipCubit', error: e);
    }
    _voipEventSubscription?.cancel();
    _networkEventSubscription?.cancel();
    return super.close();
  }
}
